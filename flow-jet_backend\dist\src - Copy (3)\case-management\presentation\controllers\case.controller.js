"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaseController = void 0;
const common_1 = require("@nestjs/common");
const case_app_service_1 = require("../../application/services/case/case.app.service");
const create_case_dto_1 = require("../../application/dtos/case/create-case.dto");
const update_case_dto_1 = require("../../application/dtos/case/update-case.dto");
let CaseController = class CaseController {
    caseAppService;
    constructor(caseAppService) {
        this.caseAppService = caseAppService;
    }
    async createCase(createCaseDto) {
        try {
            return await this.caseAppService.createCase(createCaseDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to create case.', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCaseById(id) {
        try {
            const caseDto = await this.caseAppService.getCaseById(id);
            if (!caseDto) {
                throw new common_1.HttpException('Case not found', common_1.HttpStatus.NOT_FOUND);
            }
            return caseDto;
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            throw new common_1.HttpException(error.message || `Failed to retrieve case with ID ${id}.`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateCase(id, updateCaseDto) {
        try {
            const updatedCase = await this.caseAppService.updateCase(id, updateCaseDto);
            if (!updatedCase) {
                throw new common_1.HttpException('Case not found', common_1.HttpStatus.NOT_FOUND);
            }
            return updatedCase;
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            throw new common_1.HttpException(error.message || `Failed to update case with ID ${id}.`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteCase(id) {
        try {
            const deleted = await this.caseAppService.deleteCase(id);
            if (!deleted) {
                throw new common_1.HttpException('Case not found', common_1.HttpStatus.NOT_FOUND);
            }
            return { deleted: true };
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            throw new common_1.HttpException(error.message || `Failed to delete case with ID ${id}.`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllCases() {
        try {
            return await this.caseAppService.getAllCases();
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to retrieve all cases.', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.CaseController = CaseController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_case_dto_1.CreateCaseDto]),
    __metadata("design:returntype", Promise)
], CaseController.prototype, "createCase", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CaseController.prototype, "getCaseById", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_case_dto_1.UpdateCaseDto]),
    __metadata("design:returntype", Promise)
], CaseController.prototype, "updateCase", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CaseController.prototype, "deleteCase", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CaseController.prototype, "getAllCases", null);
exports.CaseController = CaseController = __decorate([
    (0, common_1.Controller)('cases'),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [case_app_service_1.CaseAppService])
], CaseController);
//# sourceMappingURL=case.controller.js.map