{"version": 3, "file": "caseId.value-object.js", "sourceRoot": "", "sources": ["../../../../../src - Copy (3)/case-management/domain/case/caseId.value-object.ts"], "names": [], "mappings": ";;;AAMA,MAAa,MAAM;IACqB;IAApC,YAAoC,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;IAAG,CAAC;IAO9C,MAAM,CAAC,UAAU,CAAC,KAAa;QAEpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAQM,MAAM,CAAC,KAAa;QACzB,IAAI,CAAC,CAAC,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAMM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF;AApCH,wBAoCG"}