import { ICaseRepository } from '../../domain/case/case.repository.interface';
import { Case } from '../../domain/case/case.entity';
import { CaseId } from '../../domain/case/caseId.value-object';
export declare class InMemoryCaseRepository implements ICaseRepository {
    private cases;
    findById(id: CaseId): Promise<Case | null>;
    save(caseEntity: Case): Promise<void>;
    delete(id: CaseId): Promise<boolean>;
    findAll(): Promise<Case[]>;
}
