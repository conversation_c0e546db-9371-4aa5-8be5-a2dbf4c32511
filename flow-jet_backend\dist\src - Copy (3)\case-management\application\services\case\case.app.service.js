"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaseAppService = void 0;
const caseId_value_object_1 = require("../../../domain/case/caseId.value-object");
const case_entity_1 = require("../../../domain/case/case.entity");
const case_dto_1 = require("../../dtos/case/case.dto");
const uuid_1 = require("uuid");
class CaseAppService {
    caseRepository;
    constructor(caseRepository) {
        this.caseRepository = caseRepository;
    }
    async createCase(createCaseDto) {
        try {
            const newCaseId = caseId_value_object_1.CaseId.fromString((0, uuid_1.v4)());
            const newCase = case_entity_1.Case.create(newCaseId, createCaseDto.title, createCaseDto.description);
            await this.caseRepository.save(newCase);
            return this.mapCaseToDto(newCase);
        }
        catch (error) {
            console.error('Error creating case:', error);
            throw new Error('Failed to create case.');
        }
    }
    async getCaseById(id) {
        try {
            const caseId = caseId_value_object_1.CaseId.fromString(id);
            const caseEntity = await this.caseRepository.findById(caseId);
            if (!caseEntity) {
                return null;
            }
            return this.mapCaseToDto(caseEntity);
        }
        catch (error) {
            console.error(`Error getting case with ID ${id}:`, error);
            throw new Error(`Failed to retrieve case with ID ${id}.`);
        }
    }
    async updateCase(id, updateCaseDto) {
        try {
            const caseId = caseId_value_object_1.CaseId.fromString(id);
            const caseEntity = await this.caseRepository.findById(caseId);
            if (!caseEntity) {
                return null;
            }
            if (updateCaseDto.title !== undefined) {
                caseEntity.setTitle(updateCaseDto.title);
            }
            if (updateCaseDto.description !== undefined) {
                caseEntity.setDescription(updateCaseDto.description);
            }
            if (updateCaseDto.status !== undefined) {
                caseEntity.setStatus(updateCaseDto.status);
            }
            await this.caseRepository.save(caseEntity);
            return this.mapCaseToDto(caseEntity);
        }
        catch (error) {
            console.error(`Error updating case with ID ${id}:`, error);
            throw new Error(`Failed to update case with ID ${id}.`);
        }
    }
    async deleteCase(id) {
        try {
            const caseId = caseId_value_object_1.CaseId.fromString(id);
            const deleted = await this.caseRepository.delete(caseId);
            return deleted;
        }
        catch (error) {
            console.error(`Error deleting case with ID ${id}:`, error);
            throw new Error(`Failed to delete case with ID ${id}.`);
        }
    }
    async getAllCases() {
        try {
            const cases = await this.caseRepository.findAll();
            return cases.map(caseEntity => this.mapCaseToDto(caseEntity));
        }
        catch (error) {
            console.error('Error getting all cases:', error);
            throw new Error('Failed to retrieve all cases.');
        }
    }
    mapCaseToDto(caseEntity) {
        const dto = new case_dto_1.CaseDto();
        dto.id = caseEntity.id.value;
        dto.title = caseEntity.getTitle();
        dto.description = caseEntity.getDescription();
        dto.status = caseEntity.getStatus();
        dto.tasks = caseEntity.getTasks().map(task => ({
            id: task.id,
            description: task.description,
            isCompleted: task.isCompleted,
        }));
        return dto;
    }
}
exports.CaseAppService = CaseAppService;
//# sourceMappingURL=case.app.service.js.map