import { ICaseRepository } from '../../../domain/case/case.repository.interface';
import { CreateCaseDto } from '../../dtos/case/create-case.dto';
import { UpdateCaseDto } from '../../dtos/case/update-case.dto';
import { CaseDto } from '../../dtos/case/case.dto';
export declare class CaseAppService {
    private caseRepository;
    constructor(caseRepository: ICaseRepository);
    createCase(createCaseDto: CreateCaseDto): Promise<CaseDto>;
    getCaseById(id: string): Promise<CaseDto | null>;
    updateCase(id: string, updateCaseDto: UpdateCaseDto): Promise<CaseDto | null>;
    deleteCase(id: string): Promise<boolean>;
    getAllCases(): Promise<CaseDto[]>;
    private mapCaseToDto;
}
