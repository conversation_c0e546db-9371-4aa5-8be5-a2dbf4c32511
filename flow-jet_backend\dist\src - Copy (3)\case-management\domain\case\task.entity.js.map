{"version": 3, "file": "task.entity.js", "sourceRoot": "", "sources": ["../../../../../src - Copy (3)/case-management/domain/case/task.entity.ts"], "names": [], "mappings": ";;;AAGA,+BAAoC;AAMpC,MAAa,IAAI;IACC,EAAE,CAAS;IACpB,WAAW,CAAS;IACpB,WAAW,CAAU;IAE5B,YAAoB,EAAU,EAAE,WAAmB,EAAE,WAAoB;QACvE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAOM,MAAM,CAAC,MAAM,CAAC,WAAmB;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,IAAI,CAAC,IAAA,SAAM,GAAE,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAKM,QAAQ;QACb,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAKM,UAAU;QACf,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;CACF;AApCD,oBAoCC"}