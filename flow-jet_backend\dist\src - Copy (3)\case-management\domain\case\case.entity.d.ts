import { CaseId } from './caseId.value-object';
import { Task } from './task.entity';
export declare class Case {
    readonly id: CaseId;
    private title;
    private description;
    private status;
    private tasks;
    private constructor();
    static create(id: CaseId, title: string, description: string): Case;
    static load(id: CaseId, title: string, description: string, status: 'Open' | 'InProgress' | 'Closed', tasks: Task[]): Case;
    getTitle(): string;
    setTitle(title: string): void;
    getDescription(): string;
    setDescription(description: string): void;
    getStatus(): 'Open' | 'InProgress' | 'Closed';
    setStatus(status: 'Open' | 'InProgress' | 'Closed'): void;
    getTasks(): Task[];
    addTask(task: Task): void;
    removeTask(taskId: string): void;
    findTask(taskId: string): Task | undefined;
}
