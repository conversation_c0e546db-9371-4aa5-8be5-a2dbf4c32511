"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Task = void 0;
const uuid_1 = require("uuid");
class Task {
    id;
    description;
    isCompleted;
    constructor(id, description, isCompleted) {
        this.id = id;
        this.description = description;
        this.isCompleted = isCompleted;
    }
    static create(description) {
        if (!description) {
            throw new Error('Task description cannot be empty.');
        }
        return new Task((0, uuid_1.v4)(), description, false);
    }
    complete() {
        this.isCompleted = true;
    }
    incomplete() {
        this.isCompleted = false;
    }
}
exports.Task = Task;
//# sourceMappingURL=task.entity.js.map