import { CaseAppService } from '../../application/services/case/case.app.service';
import { CreateCaseDto } from '../../application/dtos/case/create-case.dto';
import { UpdateCaseDto } from '../../application/dtos/case/update-case.dto';
import { CaseDto } from '../../application/dtos/case/case.dto';
export declare class CaseController {
    private readonly caseAppService;
    constructor(caseAppService: CaseAppService);
    createCase(createCaseDto: CreateCaseDto): Promise<CaseDto>;
    getCaseById(id: string): Promise<CaseDto>;
    updateCase(id: string, updateCaseDto: UpdateCaseDto): Promise<CaseDto>;
    deleteCase(id: string): Promise<{
        deleted: boolean;
    }>;
    getAllCases(): Promise<CaseDto[]>;
}
