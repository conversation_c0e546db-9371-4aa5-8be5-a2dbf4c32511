"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaseId = void 0;
class CaseId {
    value;
    constructor(value) {
        this.value = value;
    }
    static fromString(value) {
        if (!value) {
            throw new Error('CaseId value cannot be empty.');
        }
        return new CaseId(value);
    }
    equals(other) {
        if (!(other instanceof CaseId)) {
            return false;
        }
        return this.value === other.value;
    }
    toString() {
        return this.value;
    }
}
exports.CaseId = CaseId;
//# sourceMappingURL=caseId.value-object.js.map