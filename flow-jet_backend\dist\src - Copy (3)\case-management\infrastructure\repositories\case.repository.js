"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InMemoryCaseRepository = void 0;
const task_entity_1 = require("../../domain/case/task.entity");
class InMemoryCaseRepository {
    cases = new Map();
    async findById(id) {
        const caseData = this.cases.get(id.value);
        if (!caseData) {
            return null;
        }
        const hydratedTasks = caseData.getTasks().map(taskData => {
            return task_entity_1.Task.create(taskData.description);
        });
        return caseData;
    }
    async save(caseEntity) {
        this.cases.set(caseEntity.id.value, caseEntity);
        console.log(`Case saved: ${caseEntity.id.value}`);
    }
    async delete(id) {
        const deleted = this.cases.delete(id.value);
        if (deleted) {
            console.log(`Case deleted: ${id.value}`);
        }
        else {
            console.log(`Case not found for deletion: ${id.value}`);
        }
        return deleted;
    }
    async findAll() {
        const allCases = [];
        for (const caseEntity of this.cases.values()) {
            const hydratedTasks = caseEntity.getTasks().map(taskData => task_entity_1.Task.create(taskData.description));
            allCases.push(caseEntity);
        }
        return allCases;
    }
}
exports.InMemoryCaseRepository = InMemoryCaseRepository;
//# sourceMappingURL=case.repository.js.map