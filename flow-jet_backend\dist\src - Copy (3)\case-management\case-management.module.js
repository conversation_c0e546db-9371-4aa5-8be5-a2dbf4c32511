"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaseManagementModule = void 0;
const common_1 = require("@nestjs/common");
const case_controller_1 = require("./presentation/controllers/case.controller");
const case_app_service_1 = require("./application/services/case/case.app.service");
const case_domain_service_1 = require("./domain/case/case.domain.service");
const case_repository_1 = require("./infrastructure/repositories/case.repository");
let CaseManagementModule = class CaseManagementModule {
};
exports.CaseManagementModule = CaseManagementModule;
exports.CaseManagementModule = CaseManagementModule = __decorate([
    (0, common_1.Module)({
        controllers: [case_controller_1.CaseController],
        providers: [
            {
                provide: 'ICaseRepository',
                useClass: case_repository_1.InMemoryCaseRepository,
            },
            {
                provide: case_domain_service_1.CaseDomainService,
                useFactory: (caseRepository) => new case_domain_service_1.CaseDomainService(caseRepository),
                inject: ['ICaseRepository'],
            },
            {
                provide: case_app_service_1.CaseAppService,
                useFactory: (caseRepository) => new case_app_service_1.CaseAppService(caseRepository),
                inject: ['ICaseRepository'],
            },
        ],
        exports: [
            case_app_service_1.CaseAppService,
            case_domain_service_1.CaseDomainService,
            'ICaseRepository',
        ],
    })
], CaseManagementModule);
//# sourceMappingURL=case-management.module.js.map