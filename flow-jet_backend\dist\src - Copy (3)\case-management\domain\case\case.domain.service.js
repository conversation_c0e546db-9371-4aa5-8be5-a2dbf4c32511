"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaseDomainService = void 0;
class CaseDomainService {
    caseRepository;
    constructor(caseRepository) {
        this.caseRepository = caseRepository;
    }
    async completeTaskInCase(caseId, taskId) {
        const caseEntity = await this.caseRepository.findById(caseId);
        if (!caseEntity) {
            console.warn(`Case with ID ${caseId.value} not found.`);
            return null;
        }
        const task = caseEntity.findTask(taskId);
        if (!task) {
            console.warn(`Task with ID ${taskId} not found in case ${caseId.value}.`);
            return caseEntity;
        }
        task.complete();
        await this.caseRepository.save(caseEntity);
        return caseEntity;
    }
}
exports.CaseDomainService = CaseDomainService;
//# sourceMappingURL=case.domain.service.js.map