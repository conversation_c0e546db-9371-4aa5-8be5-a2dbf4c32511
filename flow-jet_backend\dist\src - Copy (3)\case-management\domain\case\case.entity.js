"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Case = void 0;
class Case {
    id;
    title;
    description;
    status;
    tasks;
    constructor(id, title, description, status, tasks) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.status = status;
        this.tasks = tasks;
    }
    static create(id, title, description) {
        if (!title) {
            throw new Error('Case title cannot be empty.');
        }
        return new Case(id, title, description, 'Open', []);
    }
    static load(id, title, description, status, tasks) {
        if (!title || !status || !Array.isArray(tasks)) {
            throw new Error('Invalid data provided for loading case.');
        }
        return new Case(id, title, description, status, tasks);
    }
    getTitle() {
        return this.title;
    }
    setTitle(title) {
        if (!title) {
            throw new Error('Case title cannot be empty.');
        }
        this.title = title;
    }
    getDescription() {
        return this.description;
    }
    setDescription(description) {
        this.description = description;
    }
    getStatus() {
        return this.status;
    }
    setStatus(status) {
        this.status = status;
    }
    getTasks() {
        return [...this.tasks];
    }
    addTask(task) {
        if (!task) {
            throw new Error('Cannot add a null or undefined task.');
        }
        this.tasks.push(task);
    }
    removeTask(taskId) {
        const initialLength = this.tasks.length;
        this.tasks = this.tasks.filter(task => task.id !== taskId);
        if (this.tasks.length === initialLength) {
            console.warn(`Task with ID ${taskId} not found in case ${this.id.value}.`);
        }
    }
    findTask(taskId) {
        return this.tasks.find(task => task.id === taskId);
    }
}
exports.Case = Case;
//# sourceMappingURL=case.entity.js.map